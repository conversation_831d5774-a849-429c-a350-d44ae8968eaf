# Push Notification System

This Laravel application includes a comprehensive push notification system built using the **Strategy Pattern** and **Factory Pattern** with <PERSON><PERSON> as the default driver.

## Architecture

### Design Patterns Used

1. **Strategy Pattern**: Different notification drivers (Firebase, Log) implement the same interface
2. **Factory Pattern**: Creates appropriate driver instances based on configuration

### Components

- **PushNotificationDriverInterface**: Contract defining notification driver methods
- **FirebaseDriver**: Firebase Cloud Messaging implementation
- **LogDriver**: Development/testing driver that logs notifications
- **PushNotificationFactory**: Creates driver instances
- **PushNotificationService**: Main service class with high-level methods
- **PushNotificationServiceProvider**: Laravel service provider
- **PushNotification Facade**: Easy access to the service

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
PUSH_NOTIFICATION_DRIVER=firebase
FIREBASE_SERVER_KEY=your-firebase-server-key-here
FIREBASE_PROJECT_ID=your-firebase-project-id-here
LOG_PUSH_NOTIFICATIONS=true
```

### Configuration File

The system uses `config/push_notifications.php` for configuration:

```php
'default' => env('PUSH_NOTIFICATION_DRIVER', 'firebase'),
'drivers' => [
    'firebase' => [
        'server_key' => env('FIREBASE_SERVER_KEY'),
        'project_id' => env('FIREBASE_PROJECT_ID'),
    ],
    'log' => [],
],
```

## Usage Examples

### Using Dependency Injection

```php
use App\Services\PushNotification\PushNotificationService;
use App\Models\User;

class SomeController extends Controller
{
    public function __construct(
        private PushNotificationService $pushNotificationService
    ) {}

    public function sendNotification()
    {
        $user = User::find(1);
        
        $result = $this->pushNotificationService->sendToUser(
            $user,
            'Hello!',
            'This is a test notification',
            ['custom_data' => 'value']
        );
        
        if ($result['success']) {
            // Notification sent successfully
        }
    }
}
```

### Using the Facade

```php
use App\Facades\PushNotification;
use App\Models\User;

// Send to a single user
$user = User::find(1);
$result = PushNotification::sendToUser($user, 'Title', 'Body', ['data' => 'value']);

// Send to multiple users
$users = User::whereIn('id', [1, 2, 3])->get();
$result = PushNotification::sendToUsers($users, 'Title', 'Body');

// Send to device token directly
$result = PushNotification::sendToToken('device_token', 'Title', 'Body');

// Send to topic
$result = PushNotification::sendToTopic('news', 'Breaking News', 'Something happened');

// Validate token
$isValid = PushNotification::validateToken('device_token');
```

### Switching Drivers

```php
// Switch to log driver for testing
$service = app(PushNotificationService::class);
$service->switchDriver('log');

// Or with custom config
$service->switchDriver('firebase', [
    'server_key' => 'different-key'
]);
```

## API Endpoints

The system includes these API endpoints:

- `POST /api/notifications/send-to-user` - Send to specific user
- `POST /api/notifications/send-to-users` - Send to multiple users  
- `POST /api/notifications/send-to-token` - Send to device token
- `POST /api/notifications/send-to-topic` - Send to topic
- `POST /api/notifications/validate-token` - Validate token
- `POST /api/notifications/send-using-facade` - Example using facade

### Example API Request

```bash
curl -X POST http://localhost:8000/api/notifications/send-to-user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-app-token" \
  -d '{
    "user_id": 1,
    "title": "Hello!",
    "body": "This is a test notification",
    "data": {
      "action": "open_screen",
      "screen": "home"
    }
  }'
```

## Adding New Drivers

To add a new notification driver:

1. Create a new driver class implementing `PushNotificationDriverInterface`
2. Add the driver to `PushNotificationFactory::create()` method
3. Add configuration in `config/push_notifications.php`

Example:

```php
// app/Services/PushNotification/Drivers/CustomDriver.php
class CustomDriver implements PushNotificationDriverInterface
{
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array
    {
        // Implementation
    }
    
    // ... other methods
}

// In PushNotificationFactory
public static function create(string $driver, array $config = []): PushNotificationDriverInterface
{
    return match ($driver) {
        'firebase' => new FirebaseDriver($config['server_key']),
        'log' => new LogDriver(),
        'custom' => new CustomDriver($config), // Add this
        default => throw new InvalidArgumentException("Unsupported driver: {$driver}")
    };
}
```

## Testing

For testing, use the log driver:

```php
// In your test
config(['push_notifications.default' => 'log']);

// Or switch driver in test
$service = app(PushNotificationService::class);
$service->switchDriver('log');
```

## Firebase Setup

1. Create a Firebase project
2. Go to Project Settings > Cloud Messaging
3. Copy the Server Key
4. Add it to your `.env` file as `FIREBASE_SERVER_KEY`

## Error Handling

All methods return an array with this structure:

```php
[
    'success' => true|false,
    'data' => [...], // Response data on success
    'error' => 'Error message', // Error message on failure
    'driver' => 'driver_name'
]
```
