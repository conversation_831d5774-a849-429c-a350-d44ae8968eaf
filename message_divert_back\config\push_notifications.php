<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Push Notification Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default push notification driver that will be
    | used to send notifications. You may set this to any of the drivers
    | defined in the "drivers" array below.
    |
    | Supported: "firebase", "log"
    |
    */

    'default' => env('PUSH_NOTIFICATION_DRIVER', 'firebase'),

    /*
    |--------------------------------------------------------------------------
    | Push Notification Drivers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the push notification drivers for your application.
    | Each driver has its own configuration options.
    |
    */

    'drivers' => [

        'firebase' => [
            'server_key' => env('FIREBASE_SERVER_KEY'),
            'project_id' => env('FIREBASE_PROJECT_ID'),
        ],

        'log' => [
            // Log driver doesn't require any configuration
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Push Notification Options
    |--------------------------------------------------------------------------
    |
    | Here you may configure additional options for push notifications.
    |
    */

    'options' => [
        
        // Maximum number of tokens to send in a single batch
        'max_batch_size' => 1000,
        
        // Default notification priority
        'default_priority' => 'high',
        
        // Default sound for notifications
        'default_sound' => 'default',
        
        // Enable/disable notification logging
        'log_notifications' => env('LOG_PUSH_NOTIFICATIONS', true),
        
    ],

];
