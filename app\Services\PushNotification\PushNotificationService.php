<?php

namespace App\Services\PushNotification;

use App\Services\PushNotification\Contracts\PushNotificationDriverInterface;
use App\Models\User;
use Illuminate\Support\Collection;

class PushNotificationService
{
    protected PushNotificationDriverInterface $driver;

    public function __construct(PushNotificationDriverInterface $driver)
    {
        $this->driver = $driver;
    }

    /**
     * Send notification to a single user
     *
     * @param User $user
     * @param string $title
     * @param string $body
     * @param array $data
     * @return array
     */
    public function sendToUser(User $user, string $title, string $body, array $data = []): array
    {
        if (empty($user->notif_token)) {
            return [
                'success' => false,
                'error' => 'User does not have a notification token',
                'driver' => $this->driver->getDriverName(),
            ];
        }

        return $this->driver->sendToDevice($user->notif_token, $title, $body, $data);
    }

    /**
     * Send notification to multiple users
     *
     * @param Collection|array $users
     * @param string $title
     * @param string $body
     * @param array $data
     * @return array
     */
    public function sendToUsers($users, string $title, string $body, array $data = []): array
    {
        $tokens = collect($users)
            ->pluck('notif_token')
            ->filter()
            ->values()
            ->toArray();

        if (empty($tokens)) {
            return [
                'success' => false,
                'error' => 'No valid notification tokens found',
                'driver' => $this->driver->getDriverName(),
            ];
        }

        return $this->driver->sendToMultipleDevices($tokens, $title, $body, $data);
    }

    /**
     * Send notification to a device token directly
     *
     * @param string $token
     * @param string $title
     * @param string $body
     * @param array $data
     * @return array
     */
    public function sendToToken(string $token, string $title, string $body, array $data = []): array
    {
        return $this->driver->sendToDevice($token, $title, $body, $data);
    }

    /**
     * Send notification to multiple device tokens
     *
     * @param array $tokens
     * @param string $title
     * @param string $body
     * @param array $data
     * @return array
     */
    public function sendToTokens(array $tokens, string $title, string $body, array $data = []): array
    {
        return $this->driver->sendToMultipleDevices($tokens, $title, $body, $data);
    }

    /**
     * Send notification to a topic
     *
     * @param string $topic
     * @param string $title
     * @param string $body
     * @param array $data
     * @return array
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = []): array
    {
        return $this->driver->sendToTopic($topic, $title, $body, $data);
    }

    /**
     * Validate a notification token
     *
     * @param string $token
     * @return bool
     */
    public function validateToken(string $token): bool
    {
        return $this->driver->validateToken($token);
    }

    /**
     * Get the current driver
     *
     * @return PushNotificationDriverInterface
     */
    public function getDriver(): PushNotificationDriverInterface
    {
        return $this->driver;
    }

    /**
     * Switch to a different driver
     *
     * @param string $driverName
     * @param array $config
     * @return self
     */
    public function switchDriver(string $driverName, array $config = []): self
    {
        $this->driver = PushNotificationFactory::create($driverName, $config);
        return $this;
    }
}
