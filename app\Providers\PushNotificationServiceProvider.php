<?php

namespace App\Providers;

use App\Services\PushNotification\PushNotificationFactory;
use App\Services\PushNotification\PushNotificationService;
use Illuminate\Support\ServiceProvider;

class PushNotificationServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(PushNotificationService::class, function ($app) {
            $defaultDriver = config('push_notifications.default', 'firebase');
            $driverConfig = config("push_notifications.drivers.{$defaultDriver}", []);
            
            $driver = PushNotificationFactory::create($defaultDriver, $driverConfig);
            
            return new PushNotificationService($driver);
        });

        // Register alias for easier access
        $this->app->alias(PushNotificationService::class, 'push-notification');
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Publish configuration file
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/push_notifications.php' => config_path('push_notifications.php'),
            ], 'push-notification-config');
        }
    }
}
