<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{


    /**
     * Create a new user
     */
    public function store(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|unique:users,phone',
            'notif_token' => 'required|string',
            'endpoint' => 'required|string',
        ]);

        $user = User::updateOrCreate([
            'phone' => $request->phone,
        ], [
            'notif_token' => $request->notif_token,
            'endpoint' => $request->endpoint,
        ]);

        return response()->json([
            'success' => true,
            'data' => $user
        ], 201);
    }

    public function gotNotif(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|exists:users,phone',
            'data' => 'required|string',
        ]);

        return response()->json([
            'success' => true,
            'data' => 'Got notif'
        ], 200);
    }
}
