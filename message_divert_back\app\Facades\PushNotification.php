<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array sendToUser(\App\Models\User $user, string $title, string $body, array $data = [])
 * @method static array sendToUsers($users, string $title, string $body, array $data = [])
 * @method static array sendToToken(string $token, string $title, string $body, array $data = [])
 * @method static array sendToTokens(array $tokens, string $title, string $body, array $data = [])
 * @method static array sendToTopic(string $topic, string $title, string $body, array $data = [])
 * @method static bool validateToken(string $token)
 * @method static \App\Services\PushNotification\Contracts\PushNotificationDriverInterface getDriver()
 * @method static \App\Services\PushNotification\PushNotificationService switchDriver(string $driverName, array $config = [])
 *
 * @see \App\Services\PushNotification\PushNotificationService
 */
class PushNotification extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'push-notification';
    }
}
