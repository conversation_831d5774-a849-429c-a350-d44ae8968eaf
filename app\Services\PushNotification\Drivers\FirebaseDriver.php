<?php

namespace App\Services\PushNotification\Drivers;

use App\Services\PushNotification\Contracts\PushNotificationDriverInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseDriver implements PushNotificationDriverInterface
{
    protected string $serverKey;
    protected string $fcmUrl = 'https://fcm.googleapis.com/fcm/send';

    public function __construct(string $serverKey)
    {
        $this->serverKey = $serverKey;
    }

    /**
     * Send a push notification to a single device
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array
    {
        $payload = [
            'to' => $token,
            'notification' => [
                'title' => $title,
                'body' => $body,
                'sound' => 'default',
            ],
            'data' => $data,
            'priority' => 'high',
        ];

        return $this->sendRequest($payload);
    }

    /**
     * Send a push notification to multiple devices
     */
    public function sendToMultipleDevices(array $tokens, string $title, string $body, array $data = []): array
    {
        $payload = [
            'registration_ids' => $tokens,
            'notification' => [
                'title' => $title,
                'body' => $body,
                'sound' => 'default',
            ],
            'data' => $data,
            'priority' => 'high',
        ];

        return $this->sendRequest($payload);
    }

    /**
     * Send a push notification to a topic
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = []): array
    {
        $payload = [
            'to' => '/topics/' . $topic,
            'notification' => [
                'title' => $title,
                'body' => $body,
                'sound' => 'default',
            ],
            'data' => $data,
            'priority' => 'high',
        ];

        return $this->sendRequest($payload);
    }

    /**
     * Validate if a device token is valid
     */
    public function validateToken(string $token): bool
    {
        // Firebase tokens are typically 152+ characters long
        return !empty($token) && strlen($token) >= 140;
    }

    /**
     * Get the driver name
     */
    public function getDriverName(): string
    {
        return 'firebase';
    }

    /**
     * Send HTTP request to Firebase FCM
     */
    protected function sendRequest(array $payload): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'key=' . $this->serverKey,
                'Content-Type' => 'application/json',
            ])->post($this->fcmUrl, $payload);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('Firebase notification sent successfully', [
                    'response' => $responseData,
                    'payload' => $payload
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'driver' => $this->getDriverName(),
                ];
            } else {
                Log::error('Firebase notification failed', [
                    'status' => $response->status(),
                    'response' => $responseData,
                    'payload' => $payload
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['error'] ?? 'Unknown error',
                    'driver' => $this->getDriverName(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Firebase notification exception', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'driver' => $this->getDriverName(),
            ];
        }
    }
}
