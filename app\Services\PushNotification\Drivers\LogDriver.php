<?php

namespace App\Services\PushNotification\Drivers;

use App\Services\PushNotification\Contracts\PushNotificationDriverInterface;
use Illuminate\Support\Facades\Log;

class LogDriver implements PushNotificationDriverInterface
{
    /**
     * Send a push notification to a single device (logs only)
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array
    {
        $logData = [
            'type' => 'single_device',
            'token' => $token,
            'title' => $title,
            'body' => $body,
            'data' => $data,
            'driver' => $this->getDriverName(),
        ];

        Log::info('Push notification sent to device', $logData);

        return [
            'success' => true,
            'data' => $logData,
            'driver' => $this->getDriverName(),
        ];
    }

    /**
     * Send a push notification to multiple devices (logs only)
     */
    public function sendToMultipleDevices(array $tokens, string $title, string $body, array $data = []): array
    {
        $logData = [
            'type' => 'multiple_devices',
            'tokens' => $tokens,
            'token_count' => count($tokens),
            'title' => $title,
            'body' => $body,
            'data' => $data,
            'driver' => $this->getDriverName(),
        ];

        Log::info('Push notification sent to multiple devices', $logData);

        return [
            'success' => true,
            'data' => $logData,
            'driver' => $this->getDriverName(),
        ];
    }

    /**
     * Send a push notification to a topic (logs only)
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = []): array
    {
        $logData = [
            'type' => 'topic',
            'topic' => $topic,
            'title' => $title,
            'body' => $body,
            'data' => $data,
            'driver' => $this->getDriverName(),
        ];

        Log::info('Push notification sent to topic', $logData);

        return [
            'success' => true,
            'data' => $logData,
            'driver' => $this->getDriverName(),
        ];
    }

    /**
     * Validate if a device token is valid (always returns true for log driver)
     */
    public function validateToken(string $token): bool
    {
        return !empty($token);
    }

    /**
     * Get the driver name
     */
    public function getDriverName(): string
    {
        return 'log';
    }
}
