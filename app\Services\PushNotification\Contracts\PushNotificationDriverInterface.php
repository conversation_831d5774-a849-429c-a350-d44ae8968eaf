<?php

namespace App\Services\PushNotification\Contracts;

interface PushNotificationDriverInterface
{
    /**
     * Send a push notification to a single device
     *
     * @param string $token Device token
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data payload
     * @return array Response from the push notification service
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array;

    /**
     * Send a push notification to multiple devices
     *
     * @param array $tokens Array of device tokens
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data payload
     * @return array Response from the push notification service
     */
    public function sendToMultipleDevices(array $tokens, string $title, string $body, array $data = []): array;

    /**
     * Send a push notification to a topic
     *
     * @param string $topic Topic name
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data payload
     * @return array Response from the push notification service
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = []): array;

    /**
     * Validate if a device token is valid
     *
     * @param string $token Device token
     * @return bool
     */
    public function validateToken(string $token): bool;

    /**
     * Get the driver name
     *
     * @return string
     */
    public function getDriverName(): string;
}
